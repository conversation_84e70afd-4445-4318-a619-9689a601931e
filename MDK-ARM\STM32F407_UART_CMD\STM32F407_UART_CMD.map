Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    startup_stm32f407xx.o(RESET) refers to startup_stm32f407xx.o(.text) for Reset_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f407xx.o(RESET) refers to stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) for DMA2_Stream7_IRQHandler
    startup_stm32f407xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f407xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    main.o(i.SystemClock_Config) refers to memseta.o(.text) for __aeabi_memclr4
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) for HAL_RCC_OscConfig
    main.o(i.SystemClock_Config) refers to main.o(i.Error_Handler) for Error_Handler
    main.o(i.SystemClock_Config) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) for HAL_RCC_ClockConfig
    main.o(i.main) refers to usart.o(.bss) for huart1
    main.o(i.main) refers to usart.o(.data) for rxFrameFlag
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Init) for HAL_Init
    main.o(i.main) refers to main.o(i.SystemClock_Config) for SystemClock_Config
    main.o(i.main) refers to gpio.o(i.MX_GPIO_Init) for MX_GPIO_Init
    main.o(i.main) refers to dma.o(i.MX_DMA_Init) for MX_DMA_Init
    main.o(i.main) refers to usart.o(i.MX_USART1_UART_Init) for MX_USART1_UART_Init
    main.o(i.main) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    main.o(i.main) refers to stm32f4xx_hal.o(i.HAL_Delay) for HAL_Delay
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_Pos_Control) for Emm_V5_Pos_Control
    main.o(i.main) refers to emm_v5.o(i.Emm_V5_En_Control) for Emm_V5_En_Control
    dma.o(i.MX_DMA_Init) refers to dma.o(.bss) for hdma_memtomem_dma2_stream1
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    dma.o(i.MX_DMA_Init) refers to main.o(i.Error_Handler) for Error_Handler
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    dma.o(i.MX_DMA_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit) for HAL_GPIO_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) for HAL_DMA_DeInit
    usart.o(i.HAL_UART_MspDeInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ) for HAL_NVIC_DisableIRQ
    usart.o(i.HAL_UART_MspInit) refers to usart.o(.bss) for hdma_usart1_rx
    usart.o(i.HAL_UART_MspInit) refers to memseta.o(.text) for __aeabi_memclr4
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Init) for HAL_DMA_Init
    usart.o(i.HAL_UART_MspInit) refers to main.o(i.Error_Handler) for Error_Handler
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    usart.o(i.HAL_UART_MspInit) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ) for HAL_NVIC_EnableIRQ
    usart.o(i.MX_USART1_UART_Init) refers to usart.o(.bss) for huart1
    usart.o(i.MX_USART1_UART_Init) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Init) for HAL_UART_Init
    usart.o(i.MX_USART1_UART_Init) refers to main.o(i.Error_Handler) for Error_Handler
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to usart.o(.bss) for hdma_usart1_rx
    stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) refers to usart.o(.bss) for hdma_usart1_tx
    stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) for HAL_DMA_IRQHandler
    stm32f4xx_it.o(i.SysTick_Handler) refers to stm32f4xx_hal.o(i.HAL_IncTick) for HAL_IncTick
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.bss) for huart1
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to usart.o(.data) for rxCount
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) for HAL_UART_DMAStop
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) for HAL_UART_Receive_DMA
    stm32f4xx_it.o(i.USART1_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) for HAL_UART_IRQHandler
    emm_v5.o(i.Emm_V5_Auto_Return_Sys_Params_Timed) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Auto_Return_Sys_Params_Timed) refers to emm_v5.o(c.Emm_V5_Auto_Return_Sys_Params_Timed.00000032) for c.Emm_V5_Auto_Return_Sys_Params_Timed.00000032
    emm_v5.o(i.Emm_V5_Auto_Return_Sys_Params_Timed) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Auto_Return_Sys_Params_Timed) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_En_Control) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_En_Control) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_En_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_MMCL_Auto_Return_Sys_Params_Timed) refers to emm_v5.o(c.Emm_V5_MMCL_Auto_Return_Sys_Params_Timed.00000030) for c.Emm_V5_MMCL_Auto_Return_Sys_Params_Timed.00000030
    emm_v5.o(i.Emm_V5_MMCL_Auto_Return_Sys_Params_Timed) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Auto_Return_Sys_Params_Timed) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_En_Control) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_En_Control) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Origin_Interrupt) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Origin_Interrupt) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Origin_Modify_Params) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Origin_Modify_Params) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Origin_Modify_Params) refers to memseta.o(.text) for __aeabi_memclr4
    emm_v5.o(i.Emm_V5_MMCL_Origin_Set_O) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Origin_Set_O) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Origin_Trigger_Return) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Origin_Trigger_Return) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Pos_Control) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Pos_Control) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Read_Sys_Params) refers to emm_v5.o(c.Emm_V5_MMCL_Read_Sys_Params.00000020) for c.Emm_V5_MMCL_Read_Sys_Params.00000020
    emm_v5.o(i.Emm_V5_MMCL_Read_Sys_Params) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Read_Sys_Params) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Reset_Clog_Pro) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Reset_Clog_Pro) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Reset_CurPos_To_Zero) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Reset_CurPos_To_Zero) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Reset_Motor) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Reset_Motor) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Restore_Motor) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Restore_Motor) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Stop_Now) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Stop_Now) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Synchronous_motion) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Synchronous_motion) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Trig_Encoder_Cal) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Trig_Encoder_Cal) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_MMCL_Vel_Control) refers to emm_v5.o(.bss) for MMCL_cmd
    emm_v5.o(i.Emm_V5_MMCL_Vel_Control) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_DMX512_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_DMX512_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_DMX512_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_FOC_mA) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_FOC_mA) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_FOC_mA) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Firmware_Type) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Firmware_Type) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Firmware_Type) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Heart_Protect) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Heart_Protect) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Heart_Protect) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Integral_Limit) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Integral_Limit) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Integral_Limit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Lock_Btn) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Lock_Btn) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Lock_Btn) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_MicroStep) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_MicroStep) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_MicroStep) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Motor_Dir) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Motor_Dir) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Motor_Dir) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Motor_ID) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Motor_ID) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Motor_ID) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Motor_Type) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Motor_Type) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Motor_Type) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_OM_mA) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_OM_mA) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_OM_mA) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Otocp) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Otocp) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Otocp) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_PDFlag) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_PDFlag) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_PDFlag) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_PID_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_PID_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_PID_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_Pos_Window) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_Pos_Window) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_Pos_Window) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Modify_S_Vel) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Modify_S_Vel) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Modify_S_Vel) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Multi_Motor_Cmd) refers to emm_v5.o(.data) for MMCL_count
    emm_v5.o(i.Emm_V5_Multi_Motor_Cmd) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Multi_Motor_Cmd) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Multi_Motor_Cmd) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Origin_Interrupt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Origin_Modify_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Read_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Origin_Read_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Origin_Read_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Origin_Set_O) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Origin_Trigger_Return) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Pos_Control) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Pos_Control) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Pos_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_DMX512_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_DMX512_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_DMX512_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Heart_Protect) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_Heart_Protect) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_Heart_Protect) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Integral_Limit) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_Integral_Limit) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_Integral_Limit) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Motor_Conf_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_Motor_Conf_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_Motor_Conf_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Opt_Param_Sta) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_Opt_Param_Sta) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_Opt_Param_Sta) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Otocp) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_Otocp) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_Otocp) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_PID_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_PID_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_PID_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Pos_Window) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_Pos_Window) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_Pos_Window) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to emm_v5.o(c.Emm_V5_Read_Sys_Params.0000001a) for c.Emm_V5_Read_Sys_Params.0000001a
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_Sys_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Read_System_State_Params) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Read_System_State_Params) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Read_System_State_Params) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Reset_Clog_Pro) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Reset_Motor) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Reset_Motor) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Reset_Motor) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Restore_Motor) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Restore_Motor) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Restore_Motor) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Stop_Now) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Stop_Now) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Stop_Now) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Synchronous_motion) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Trig_Encoder_Cal) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Trig_Encoder_Cal) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Trig_Encoder_Cal) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    emm_v5.o(i.Emm_V5_Vel_Control) refers to emm_v5.o(.bss) for cmd
    emm_v5.o(i.Emm_V5_Vel_Control) refers to usart.o(.bss) for huart1
    emm_v5.o(i.Emm_V5_Vel_Control) refers to stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) for HAL_UART_Transmit_DMA
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_LIN_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) for UART_DMARxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_GetError) for HAL_DMA_GetError
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) for UART_DMATxOnlyAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) for UART_DMATxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) for UART_DMARxAbortCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_DeInit) refers to usart.o(i.HAL_UART_MspDeInit) for HAL_UART_MspDeInit
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) for UART_DMAAbortOnError
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Receive_IT) for UART_Receive_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT) for HAL_DMA_Abort_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_Transmit_IT) for UART_Transmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler) refers to stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) for UART_EndTransmit_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to usart.o(i.HAL_UART_MspInit) for HAL_UART_MspInit
    stm32f4xx_hal_uart.o(i.HAL_UART_Init) refers to stm32f4xx_hal_uart.o(i.UART_SetConfig) for UART_SetConfig
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) for UART_DMAReceiveCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) for UART_DMARxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit) refers to stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) for UART_WaitOnFlagUntilTimeout
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) for UART_DMATransmitCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) for UART_DMATxHalfCplt
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_uart.o(i.UART_DMAError) for UART_DMAError
    stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) for HAL_DMA_Start_IT
    stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndTxTransfer) for UART_EndTxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.UART_EndRxTransfer) for UART_EndRxTransfer
    stm32f4xx_hal_uart.o(i.UART_DMAError) refers to stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback) for HAL_UART_ErrorCallback
    stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback) for HAL_UART_RxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback) for HAL_UART_AbortReceiveCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback) for HAL_UART_AbortCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback) for HAL_UART_TxHalfCpltCallback
    stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback) refers to stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback) for HAL_UART_AbortTransmitCpltCallback
    stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback) for HAL_UART_TxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_Receive_IT) refers to stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback) for HAL_UART_RxCpltCallback
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) for HAL_RCC_GetPCLK2Freq
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_uart.o(i.UART_SetConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) for HAL_RCC_GetPCLK1Freq
    stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) for HAL_RCC_GetSysClockFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to system_stm32f4xx.o(.constdata) for APBPrescTable
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq) for HAL_RCC_GetHCLKFreq
    stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq) refers to uldiv.o(.text) for __aeabi_uldivmod
    stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init) for HAL_GPIO_Init
    stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler) refers to stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback) for HAL_RCC_CSSCallback
    stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode) for FLASH_SetErrorCode
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback) for HAL_FLASH_OperationErrorCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback) for HAL_FLASH_EndOfOperationCallback
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Byte) for FLASH_Program_Byte
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord) for FLASH_Program_HalfWord
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_Word) for FLASH_Program_Word
    stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT) refers to stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord) for FLASH_Program_DoubleWord
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches) for FLASH_FlushCaches
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase) for FLASH_MassErase
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector) for FLASH_Erase_Sector
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP) for FLASH_OB_GetWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP) for FLASH_OB_GetRDP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser) for FLASH_OB_GetUser
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR) for FLASH_OB_GetBOR
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash.o(.bss) for pFlash
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP) for FLASH_OB_EnableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP) for FLASH_OB_DisableWRP
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig) for FLASH_OB_RDP_LevelConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig) for FLASH_OB_UserConfig
    stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram) refers to stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig) for FLASH_OB_BOR_LevelConfig
    stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler) refers to stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback) for HAL_GPIO_EXTI_Callback
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT) refers to stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig) for DMA_MultiBufferSetConfig
    stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) refers to stm32f4xx_hal_dma.o(.constdata) for flagBitshiftOffset
    stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam) for DMA_CheckFifoParam
    stm32f4xx_hal_dma.o(i.HAL_DMA_Init) refers to stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift) for DMA_CalcBaseAndBitshift
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer) refers to stm32f4xx_hal_dma.o(i.HAL_DMA_Abort) for HAL_DMA_Abort
    stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback) refers to stm32f4xx_hal_dma.o(c.HAL_DMA_RegisterCallback.00000028) for c.HAL_DMA_RegisterCallback.00000028
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT) refers to stm32f4xx_hal_dma.o(i.DMA_SetConfig) for DMA_SetConfig
    stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback) refers to stm32f4xx_hal_dma.o(c.HAL_DMA_UnRegisterCallback.00000026) for c.HAL_DMA_UnRegisterCallback.00000026
    stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler) refers to stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback) for HAL_PWR_PVDCallback
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping) for __NVIC_GetPriorityGrouping
    stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) refers to stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority) for __NVIC_SetPriority
    stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback) for HAL_SYSTICK_Callback
    stm32f4xx_hal.o(i.HAL_DeInit) refers to stm32f4xx_hal.o(i.HAL_MspDeInit) for HAL_MspDeInit
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_Delay) refers to stm32f4xx_hal.o(i.HAL_GetTick) for HAL_GetTick
    stm32f4xx_hal.o(i.HAL_GetTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_GetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_GetTickPrio) refers to stm32f4xx_hal.o(.data) for uwTickPrio
    stm32f4xx_hal.o(i.HAL_IncTick) refers to stm32f4xx_hal.o(.data) for uwTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping) for HAL_NVIC_SetPriorityGrouping
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    stm32f4xx_hal.o(i.HAL_Init) refers to stm32f4xx_hal_msp.o(i.HAL_MspInit) for HAL_MspInit
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_InitTick) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config) for HAL_SYSTICK_Config
    stm32f4xx_hal.o(i.HAL_InitTick) refers to stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority) for HAL_NVIC_SetPriority
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(.data) for uwTickFreq
    stm32f4xx_hal.o(i.HAL_SetTickFreq) refers to stm32f4xx_hal.o(i.HAL_InitTick) for HAL_InitTick
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.constdata) for AHBPrescTable
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f407xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f407xx.o(HEAP), (1024 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing gpio.o(.rev16_text), (4 bytes).
    Removing gpio.o(.revsh_text), (4 bytes).
    Removing gpio.o(.rrx_text), (6 bytes).
    Removing dma.o(.rev16_text), (4 bytes).
    Removing dma.o(.revsh_text), (4 bytes).
    Removing dma.o(.rrx_text), (6 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i.HAL_UART_MspDeInit), (76 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_msp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_msp.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(.rev16_text), (4 bytes).
    Removing emm_v5.o(.revsh_text), (4 bytes).
    Removing emm_v5.o(.rrx_text), (6 bytes).
    Removing emm_v5.o(i.Emm_V5_Auto_Return_Sys_Params_Timed), (440 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Auto_Return_Sys_Params_Timed), (374 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_En_Control), (108 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Origin_Interrupt), (100 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Origin_Modify_Params), (228 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Origin_Set_O), (104 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Origin_Trigger_Return), (102 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Pos_Control), (190 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Read_Sys_Params), (338 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Reset_Clog_Pro), (100 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Reset_CurPos_To_Zero), (100 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Reset_Motor), (100 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Restore_Motor), (100 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Stop_Now), (104 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Synchronous_motion), (100 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Trig_Encoder_Cal), (100 bytes).
    Removing emm_v5.o(i.Emm_V5_MMCL_Vel_Control), (122 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Ctrl_Mode), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_DMX512_Params), (134 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_FOC_mA), (64 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Firmware_Type), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Heart_Protect), (72 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Integral_Limit), (72 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Lock_Btn), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_MicroStep), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Motor_Dir), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Motor_ID), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Motor_Type), (74 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_OM_mA), (64 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Otocp), (86 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_PDFlag), (52 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_PID_Params), (110 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_Pos_Window), (64 bytes).
    Removing emm_v5.o(i.Emm_V5_Modify_S_Vel), (60 bytes).
    Removing emm_v5.o(i.Emm_V5_Multi_Motor_Cmd), (178 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Interrupt), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Modify_Params), (134 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Read_Params), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Set_O), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Origin_Trigger_Return), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_DMX512_Params), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Heart_Protect), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Integral_Limit), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Motor_Conf_Params), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Opt_Param_Sta), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Otocp), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_PID_Params), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Pos_Window), (46 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_Sys_Params), (400 bytes).
    Removing emm_v5.o(i.Emm_V5_Read_System_State_Params), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Clog_Pro), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_CurPos_To_Zero), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Reset_Motor), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Restore_Motor), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Stop_Now), (56 bytes).
    Removing emm_v5.o(i.Emm_V5_Synchronous_motion), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Trig_Encoder_Cal), (50 bytes).
    Removing emm_v5.o(i.Emm_V5_Vel_Control), (78 bytes).
    Removing emm_v5.o(.data), (2 bytes).
    Removing emm_v5.o(c.Emm_V5_Auto_Return_Sys_Params_Timed.00000032), (18 bytes).
    Removing emm_v5.o(c.Emm_V5_MMCL_Auto_Return_Sys_Params_Timed.00000030), (18 bytes).
    Removing emm_v5.o(c.Emm_V5_MMCL_Read_Sys_Params.00000020), (18 bytes).
    Removing emm_v5.o(c.Emm_V5_Read_Sys_Params.0000001a), (18 bytes).
    Removing stm32f4xx_hal_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_tim_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_uart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableReceiver), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_EnableTransmitter), (68 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_HalfDuplex_Init), (118 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_Init), (142 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_LIN_SendBreak), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_EnterMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_ExitMuteMode), (62 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_MultiProcessor_Init), (154 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort), (170 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive), (102 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceiveCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortReceive_IT), (122 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit), (90 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmitCpltCallback), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_AbortTransmit_IT), (110 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Abort_IT), (228 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAPause), (124 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DMAResume), (126 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_DeInit), (58 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetError), (6 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_GetState), (20 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_MspInit), (2 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive), (224 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Receive_IT), (112 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit), (216 bytes).
    Removing stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_IT), (88 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxAbortCallback), (52 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMARxOnlyAbortCallback), (24 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxAbortCallback), (52 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_DMATxOnlyAbortCallback), (24 bytes).
    Removing stm32f4xx_hal_uart.o(i.UART_WaitOnFlagUntilTimeout), (108 bytes).
    Removing stm32f4xx_hal_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_CSSCallback), (2 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DeInit), (4 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_DisableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_EnableCSS), (12 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetClockConfig), (88 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_GetOscConfig), (300 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_MCOConfig), (230 bytes).
    Removing stm32f4xx_hal_rcc.o(i.HAL_RCC_NMI_IRQHandler), (38 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_DisablePLLI2S), (56 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_EnablePLLI2S), (128 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKConfig), (70 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_GetPeriphCLKFreq), (170 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCCEx_PeriphCLKConfig), (496 bytes).
    Removing stm32f4xx_hal_rcc_ex.o(i.HAL_RCC_DeInit), (504 bytes).
    Removing stm32f4xx_hal_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Byte), (44 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_DoubleWord), (68 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_HalfWord), (48 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_Program_Word), (48 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_SetErrorCode), (262 bytes).
    Removing stm32f4xx_hal_flash.o(i.FLASH_WaitForLastOperation), (120 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_EndOfOperationCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_GetError), (12 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_IRQHandler), (402 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Lock), (28 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Launch), (36 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Lock), (28 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OB_Unlock), (54 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_OperationErrorCallback), (2 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program), (172 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Program_IT), (156 bytes).
    Removing stm32f4xx_hal_flash.o(i.HAL_FLASH_Unlock), (66 bytes).
    Removing stm32f4xx_hal_flash.o(.bss), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_Erase_Sector), (104 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_FlushCaches), (150 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_MassErase), (60 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_BOR_LevelConfig), (38 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_DisableWRP), (48 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_EnableWRP), (46 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetRDP), (42 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_RDP_LevelConfig), (32 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.FLASH_OB_UserConfig), (66 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase), (206 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_Erase_IT), (148 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBGetConfig), (34 bytes).
    Removing stm32f4xx_hal_flash_ex.o(i.HAL_FLASHEx_OBProgram), (162 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_DeInit), (392 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_Callback), (2 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_EXTI_IRQHandler), (36 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_LockPin), (46 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_ReadPin), (16 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_TogglePin), (18 bytes).
    Removing stm32f4xx_hal_gpio.o(i.HAL_GPIO_WritePin), (12 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.DMA_MultiBufferSetConfig), (32 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_ChangeMemory), (18 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart), (124 bytes).
    Removing stm32f4xx_hal_dma_ex.o(i.HAL_DMAEx_MultiBufferStart_IT), (5238 bytes).
    Removing stm32f4xx_hal_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_DeInit), (112 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetError), (6 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_GetState), (8 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_PollForTransfer), (346 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_RegisterCallback), (96 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_Start), (102 bytes).
    Removing stm32f4xx_hal_dma.o(i.HAL_DMA_UnRegisterCallback), (124 bytes).
    Removing stm32f4xx_hal_dma.o(c.HAL_DMA_RegisterCallback.00000028), (6 bytes).
    Removing stm32f4xx_hal_dma.o(c.HAL_DMA_UnRegisterCallback.00000026), (7 bytes).
    Removing stm32f4xx_hal_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_ConfigPVD), (240 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DeInit), (36 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableBkUpAccess), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSEVOnPend), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableSleepOnExit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_DisableWakeUpPin), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableBkUpAccess), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnablePVD), (12 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSEVOnPend), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableSleepOnExit), (26 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnableWakeUpPin), (24 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSLEEPMode), (40 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTANDBYMode), (56 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_EnterSTOPMode), (90 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVDCallback), (2 bytes).
    Removing stm32f4xx_hal_pwr.o(i.HAL_PWR_PVD_IRQHandler), (38 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_ControlVoltageScaling), (142 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_DisableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableBkUpReg), (60 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_EnableFlashPowerDown), (12 bytes).
    Removing stm32f4xx_hal_pwr_ex.o(i.HAL_PWREx_GetVoltageRange), (16 bytes).
    Removing stm32f4xx_hal_cortex.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_cortex.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_ConfigRegion), (102 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Disable), (54 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_MPU_Enable), (72 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_ClearPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_DisableIRQ), (68 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetActive), (50 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPendingIRQ), (50 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriority), (150 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_GetPriorityGrouping), (8 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPendingIRQ), (36 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_NVIC_SystemReset), (76 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_CLKSourceConfig), (40 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Callback), (2 bytes).
    Removing stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_IRQHandler), (8 bytes).
    Removing stm32f4xx_hal.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGSleepMode), (26 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStandbyMode), (26 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_DisableDBGStopMode), (26 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGSleepMode), (26 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStandbyMode), (26 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DBGMCU_EnableDBGStopMode), (26 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DeInit), (74 bytes).
    Removing stm32f4xx_hal.o(i.HAL_DisableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_EnableCompensationCell), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetDEVID), (16 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetHalVersion), (10 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetREVID), (14 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickFreq), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetTickPrio), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw0), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw1), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_GetUIDw2), (12 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspDeInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_MspInit), (2 bytes).
    Removing stm32f4xx_hal.o(i.HAL_ResumeTick), (18 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SetTickFreq), (64 bytes).
    Removing stm32f4xx_hal.o(i.HAL_SuspendTick), (18 bytes).
    Removing stm32f4xx_hal_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hal_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearConfigLine), (136 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_ClearPending), (28 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GenerateSWI), (24 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetConfigLine), (192 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetHandle), (14 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_GetPending), (36 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_IRQHandler), (52 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_RegisterCallback), (20 bytes).
    Removing stm32f4xx_hal_exti.o(i.HAL_EXTI_SetConfigLine), (320 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (274 bytes).

307 unused section(s) (total 25039 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ../Drivers/STM32F4xx_HAL_Driver/Src/stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ../Src/dma.c                             0x00000000   Number         0  dma.o ABSOLUTE
    ../Src/gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ../Src/main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ../Src/stm32f4xx_hal_msp.c               0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ../Src/stm32f4xx_it.c                    0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ../Src/system_stm32f4xx.c                0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ../Src/usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal.c 0x00000000   Number         0  stm32f4xx_hal.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_cortex.c 0x00000000   Number         0  stm32f4xx_hal_cortex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma.c 0x00000000   Number         0  stm32f4xx_hal_dma.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_dma_ex.c 0x00000000   Number         0  stm32f4xx_hal_dma_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_exti.c 0x00000000   Number         0  stm32f4xx_hal_exti.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash.c 0x00000000   Number         0  stm32f4xx_hal_flash.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ex.c 0x00000000   Number         0  stm32f4xx_hal_flash_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_hal_flash_ramfunc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_gpio.c 0x00000000   Number         0  stm32f4xx_hal_gpio.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr.c 0x00000000   Number         0  stm32f4xx_hal_pwr.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_pwr_ex.c 0x00000000   Number         0  stm32f4xx_hal_pwr_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc.c 0x00000000   Number         0  stm32f4xx_hal_rcc.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_rcc_ex.c 0x00000000   Number         0  stm32f4xx_hal_rcc_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim.c 0x00000000   Number         0  stm32f4xx_hal_tim.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_tim_ex.c 0x00000000   Number         0  stm32f4xx_hal_tim_ex.o ABSOLUTE
    ..\Drivers\STM32F4xx_HAL_Driver\Src\stm32f4xx_hal_uart.c 0x00000000   Number         0  stm32f4xx_hal_uart.o ABSOLUTE
    ..\Src\Emm_V5.c                          0x00000000   Number         0  emm_v5.o ABSOLUTE
    ..\Src\dma.c                             0x00000000   Number         0  dma.o ABSOLUTE
    ..\Src\gpio.c                            0x00000000   Number         0  gpio.o ABSOLUTE
    ..\Src\main.c                            0x00000000   Number         0  main.o ABSOLUTE
    ..\Src\stm32f4xx_hal_msp.c               0x00000000   Number         0  stm32f4xx_hal_msp.o ABSOLUTE
    ..\Src\stm32f4xx_it.c                    0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\Src\system_stm32f4xx.c                0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\Src\usart.c                           0x00000000   Number         0  usart.o ABSOLUTE
    ..\\Src\\Emm_V5.c                        0x00000000   Number         0  emm_v5.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    startup_stm32f407xx.s                    0x00000000   Number         0  startup_stm32f407xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f407xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section       36  startup_stm32f407xx.o(.text)
    $v0                                      0x080001a0   Number         0  startup_stm32f407xx.o(.text)
    .text                                    0x080001c4   Section        0  uldiv.o(.text)
    .text                                    0x08000226   Section        0  memseta.o(.text)
    .text                                    0x0800024a   Section        0  llshl.o(.text)
    .text                                    0x08000268   Section        0  llushr.o(.text)
    .text                                    0x08000288   Section       36  init.o(.text)
    i.BusFault_Handler                       0x080002ac   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA2_Stream2_IRQHandler                0x080002b0   Section        0  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    i.DMA2_Stream7_IRQHandler                0x080002c0   Section        0  stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler)
    i.DMA_CalcBaseAndBitshift                0x080002d0   Section        0  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    DMA_CalcBaseAndBitshift                  0x080002d1   Thumb Code    52  stm32f4xx_hal_dma.o(i.DMA_CalcBaseAndBitshift)
    i.DMA_CheckFifoParam                     0x08000304   Section        0  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    DMA_CheckFifoParam                       0x08000305   Thumb Code   170  stm32f4xx_hal_dma.o(i.DMA_CheckFifoParam)
    i.DMA_SetConfig                          0x080003ae   Section        0  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    DMA_SetConfig                            0x080003af   Thumb Code    44  stm32f4xx_hal_dma.o(i.DMA_SetConfig)
    i.DebugMon_Handler                       0x080003da   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.Emm_V5_En_Control                      0x080003dc   Section        0  emm_v5.o(i.Emm_V5_En_Control)
    i.Emm_V5_Pos_Control                     0x08000418   Section        0  emm_v5.o(i.Emm_V5_Pos_Control)
    i.Error_Handler                          0x0800047e   Section        0  main.o(i.Error_Handler)
    i.HAL_DMA_Abort                          0x08000482   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    i.HAL_DMA_Abort_IT                       0x0800052c   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    i.HAL_DMA_IRQHandler                     0x08000554   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    i.HAL_DMA_Init                           0x08000794   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    i.HAL_DMA_Start_IT                       0x08000882   Section        0  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    i.HAL_Delay                              0x08000914   Section        0  stm32f4xx_hal.o(i.HAL_Delay)
    i.HAL_GPIO_Init                          0x0800093e   Section        0  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    i.HAL_GetTick                            0x08000b70   Section        0  stm32f4xx_hal.o(i.HAL_GetTick)
    i.HAL_IncTick                            0x08000b7c   Section        0  stm32f4xx_hal.o(i.HAL_IncTick)
    i.HAL_Init                               0x08000b9e   Section        0  stm32f4xx_hal.o(i.HAL_Init)
    i.HAL_InitTick                           0x08000be0   Section        0  stm32f4xx_hal.o(i.HAL_InitTick)
    i.HAL_MspInit                            0x08000c32   Section        0  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    i.HAL_NVIC_EnableIRQ                     0x08000c82   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    i.HAL_NVIC_SetPriority                   0x08000ca2   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    i.HAL_NVIC_SetPriorityGrouping           0x08000d1e   Section        0  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    i.HAL_RCC_ClockConfig                    0x08000d4e   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    i.HAL_RCC_GetHCLKFreq                    0x08000f48   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    i.HAL_RCC_GetPCLK1Freq                   0x08000f54   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    i.HAL_RCC_GetPCLK2Freq                   0x08000f76   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    i.HAL_RCC_GetSysClockFreq                0x08000f98   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    i.HAL_RCC_OscConfig                      0x0800106c   Section        0  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    i.HAL_SYSTICK_Config                     0x080015d0   Section        0  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    i.HAL_UART_DMAStop                       0x08001604   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    i.HAL_UART_ErrorCallback                 0x0800166a   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    i.HAL_UART_IRQHandler                    0x0800166c   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    i.HAL_UART_Init                          0x080017c2   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    i.HAL_UART_MspInit                       0x08001834   Section        0  usart.o(i.HAL_UART_MspInit)
    i.HAL_UART_Receive_DMA                   0x08001996   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    i.HAL_UART_RxCpltCallback                0x08001a5e   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    i.HAL_UART_RxHalfCpltCallback            0x08001a60   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    i.HAL_UART_Transmit_DMA                  0x08001a62   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    i.HAL_UART_TxCpltCallback                0x08001b02   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    i.HAL_UART_TxHalfCpltCallback            0x08001b04   Section        0  stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    i.HardFault_Handler                      0x08001b06   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MX_DMA_Init                            0x08001b0a   Section        0  dma.o(i.MX_DMA_Init)
    i.MX_GPIO_Init                           0x08001ba6   Section        0  gpio.o(i.MX_GPIO_Init)
    i.MX_USART1_UART_Init                    0x08001c12   Section        0  usart.o(i.MX_USART1_UART_Init)
    i.MemManage_Handler                      0x08001c52   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001c56   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.PendSV_Handler                         0x08001c58   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.SVC_Handler                            0x08001c5a   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SysTick_Handler                        0x08001c5c   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SystemClock_Config                     0x08001c64   Section        0  main.o(i.SystemClock_Config)
    i.SystemInit                             0x08001d26   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.UART_DMAAbortOnError                   0x08001d4e   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    UART_DMAAbortOnError                     0x08001d4f   Thumb Code    20  stm32f4xx_hal_uart.o(i.UART_DMAAbortOnError)
    i.UART_DMAError                          0x08001d62   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAError)
    UART_DMAError                            0x08001d63   Thumb Code    80  stm32f4xx_hal_uart.o(i.UART_DMAError)
    i.UART_DMAReceiveCplt                    0x08001db2   Section        0  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    UART_DMAReceiveCplt                      0x08001db3   Thumb Code    70  stm32f4xx_hal_uart.o(i.UART_DMAReceiveCplt)
    i.UART_DMARxHalfCplt                     0x08001df8   Section        0  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    UART_DMARxHalfCplt                       0x08001df9   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMARxHalfCplt)
    i.UART_DMATransmitCplt                   0x08001e06   Section        0  stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt)
    UART_DMATransmitCplt                     0x08001e07   Thumb Code    54  stm32f4xx_hal_uart.o(i.UART_DMATransmitCplt)
    i.UART_DMATxHalfCplt                     0x08001e3c   Section        0  stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt)
    UART_DMATxHalfCplt                       0x08001e3d   Thumb Code    14  stm32f4xx_hal_uart.o(i.UART_DMATxHalfCplt)
    i.UART_EndRxTransfer                     0x08001e4a   Section        0  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    UART_EndRxTransfer                       0x08001e4b   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndRxTransfer)
    i.UART_EndTransmit_IT                    0x08001e6a   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    UART_EndTransmit_IT                      0x08001e6b   Thumb Code    32  stm32f4xx_hal_uart.o(i.UART_EndTransmit_IT)
    i.UART_EndTxTransfer                     0x08001e8a   Section        0  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    UART_EndTxTransfer                       0x08001e8b   Thumb Code    20  stm32f4xx_hal_uart.o(i.UART_EndTxTransfer)
    i.UART_Receive_IT                        0x08001e9e   Section        0  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    UART_Receive_IT                          0x08001e9f   Thumb Code   166  stm32f4xx_hal_uart.o(i.UART_Receive_IT)
    i.UART_SetConfig                         0x08001f44   Section        0  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    UART_SetConfig                           0x08001f45   Thumb Code  1044  stm32f4xx_hal_uart.o(i.UART_SetConfig)
    i.UART_Transmit_IT                       0x08002358   Section        0  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    UART_Transmit_IT                         0x08002359   Thumb Code   104  stm32f4xx_hal_uart.o(i.UART_Transmit_IT)
    i.USART1_IRQHandler                      0x080023c0   Section        0  stm32f4xx_it.o(i.USART1_IRQHandler)
    i.UsageFault_Handler                     0x08002450   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__NVIC_GetPriorityGrouping             0x08002454   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    __NVIC_GetPriorityGrouping               0x08002455   Thumb Code    16  stm32f4xx_hal_cortex.o(i.__NVIC_GetPriorityGrouping)
    i.__NVIC_SetPriority                     0x08002464   Section        0  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    __NVIC_SetPriority                       0x08002465   Thumb Code    44  stm32f4xx_hal_cortex.o(i.__NVIC_SetPriority)
    i.__scatterload_copy                     0x08002490   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800249e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x080024a0   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.main                                   0x080024ae   Section        0  main.o(i.main)
    .constdata                               0x080025b4   Section        8  stm32f4xx_hal_dma.o(.constdata)
    flagBitshiftOffset                       0x080025b4   Data           8  stm32f4xx_hal_dma.o(.constdata)
    .constdata                               0x080025bc   Section       24  system_stm32f4xx.o(.constdata)
    .data                                    0x20000000   Section        2  usart.o(.data)
    .data                                    0x20000004   Section        9  stm32f4xx_hal.o(.data)
    .data                                    0x20000010   Section        4  system_stm32f4xx.o(.data)
    .bss                                     0x20000014   Section       96  dma.o(.bss)
    .bss                                     0x20000074   Section      512  usart.o(.bss)
    .bss                                     0x20000274   Section     2260  emm_v5.o(.bss)
    cmd                                      0x20000674   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000684   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000694   Data          16  emm_v5.o(.bss)
    cmd                                      0x200006a4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200006b4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200006c4   Data         512  emm_v5.o(.bss)
    cmd                                      0x200008c4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200008d4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200008e4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200008f4   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000904   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000914   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000924   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000934   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000944   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000954   Data          32  emm_v5.o(.bss)
    cmd                                      0x20000974   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000984   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000994   Data          16  emm_v5.o(.bss)
    cmd                                      0x200009a4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200009b4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200009c4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200009d4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200009e4   Data          16  emm_v5.o(.bss)
    cmd                                      0x200009f4   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a04   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a14   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a24   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a34   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a44   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a54   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a64   Data          20  emm_v5.o(.bss)
    cmd                                      0x20000a78   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000a88   Data          32  emm_v5.o(.bss)
    cmd                                      0x20000aa8   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000ab8   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000ac8   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000ad8   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000ae8   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000af8   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000b08   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000b18   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000b28   Data          16  emm_v5.o(.bss)
    cmd                                      0x20000b38   Data          16  emm_v5.o(.bss)
    STACK                                    0x20000b48   Section     4096  startup_stm32f407xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OSPACE$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f407xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f407xx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f407xx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    Reset_Handler                            0x080001a1   Thumb Code     8  startup_stm32f407xx.o(.text)
    ADC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DCMI_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream0_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream1_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI0_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI1_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI2_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI3_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FLASH_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FMC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    FPU_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    PVD_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RCC_IRQHandler                           0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SDIO_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI1_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    SPI3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM2_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM3_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM4_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM5_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM7_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART4_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    UART5_IRQHandler                         0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART2_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART3_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    USART6_IRQHandler                        0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    WWDG_IRQHandler                          0x080001bb   Thumb Code     0  startup_stm32f407xx.o(.text)
    __aeabi_uldivmod                         0x080001c5   Thumb Code    98  uldiv.o(.text)
    __aeabi_memset                           0x08000227   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x08000227   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x08000227   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x08000235   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x08000235   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x08000235   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x08000239   Thumb Code    18  memseta.o(.text)
    __aeabi_llsl                             0x0800024b   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x0800024b   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000269   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000269   Thumb Code     0  llushr.o(.text)
    __scatterload                            0x08000289   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08000289   Thumb Code     0  init.o(.text)
    BusFault_Handler                         0x080002ad   Thumb Code     4  stm32f4xx_it.o(i.BusFault_Handler)
    DMA2_Stream2_IRQHandler                  0x080002b1   Thumb Code    16  stm32f4xx_it.o(i.DMA2_Stream2_IRQHandler)
    DMA2_Stream7_IRQHandler                  0x080002c1   Thumb Code    16  stm32f4xx_it.o(i.DMA2_Stream7_IRQHandler)
    DebugMon_Handler                         0x080003db   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    Emm_V5_En_Control                        0x080003dd   Thumb Code    60  emm_v5.o(i.Emm_V5_En_Control)
    Emm_V5_Pos_Control                       0x08000419   Thumb Code   102  emm_v5.o(i.Emm_V5_Pos_Control)
    Error_Handler                            0x0800047f   Thumb Code     4  main.o(i.Error_Handler)
    HAL_DMA_Abort                            0x08000483   Thumb Code   170  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort)
    HAL_DMA_Abort_IT                         0x0800052d   Thumb Code    40  stm32f4xx_hal_dma.o(i.HAL_DMA_Abort_IT)
    HAL_DMA_IRQHandler                       0x08000555   Thumb Code   576  stm32f4xx_hal_dma.o(i.HAL_DMA_IRQHandler)
    HAL_DMA_Init                             0x08000795   Thumb Code   238  stm32f4xx_hal_dma.o(i.HAL_DMA_Init)
    HAL_DMA_Start_IT                         0x08000883   Thumb Code   146  stm32f4xx_hal_dma.o(i.HAL_DMA_Start_IT)
    HAL_Delay                                0x08000915   Thumb Code    42  stm32f4xx_hal.o(i.HAL_Delay)
    HAL_GPIO_Init                            0x0800093f   Thumb Code   562  stm32f4xx_hal_gpio.o(i.HAL_GPIO_Init)
    HAL_GetTick                              0x08000b71   Thumb Code    12  stm32f4xx_hal.o(i.HAL_GetTick)
    HAL_IncTick                              0x08000b7d   Thumb Code    34  stm32f4xx_hal.o(i.HAL_IncTick)
    HAL_Init                                 0x08000b9f   Thumb Code    66  stm32f4xx_hal.o(i.HAL_Init)
    HAL_InitTick                             0x08000be1   Thumb Code    82  stm32f4xx_hal.o(i.HAL_InitTick)
    HAL_MspInit                              0x08000c33   Thumb Code    80  stm32f4xx_hal_msp.o(i.HAL_MspInit)
    HAL_NVIC_EnableIRQ                       0x08000c83   Thumb Code    32  stm32f4xx_hal_cortex.o(i.HAL_NVIC_EnableIRQ)
    HAL_NVIC_SetPriority                     0x08000ca3   Thumb Code   124  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriority)
    HAL_NVIC_SetPriorityGrouping             0x08000d1f   Thumb Code    48  stm32f4xx_hal_cortex.o(i.HAL_NVIC_SetPriorityGrouping)
    HAL_RCC_ClockConfig                      0x08000d4f   Thumb Code   506  stm32f4xx_hal_rcc.o(i.HAL_RCC_ClockConfig)
    HAL_RCC_GetHCLKFreq                      0x08000f49   Thumb Code    12  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetHCLKFreq)
    HAL_RCC_GetPCLK1Freq                     0x08000f55   Thumb Code    34  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK1Freq)
    HAL_RCC_GetPCLK2Freq                     0x08000f77   Thumb Code    34  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetPCLK2Freq)
    HAL_RCC_GetSysClockFreq                  0x08000f99   Thumb Code   212  stm32f4xx_hal_rcc.o(i.HAL_RCC_GetSysClockFreq)
    HAL_RCC_OscConfig                        0x0800106d   Thumb Code  1380  stm32f4xx_hal_rcc.o(i.HAL_RCC_OscConfig)
    HAL_SYSTICK_Config                       0x080015d1   Thumb Code    52  stm32f4xx_hal_cortex.o(i.HAL_SYSTICK_Config)
    HAL_UART_DMAStop                         0x08001605   Thumb Code   102  stm32f4xx_hal_uart.o(i.HAL_UART_DMAStop)
    HAL_UART_ErrorCallback                   0x0800166b   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_ErrorCallback)
    HAL_UART_IRQHandler                      0x0800166d   Thumb Code   342  stm32f4xx_hal_uart.o(i.HAL_UART_IRQHandler)
    HAL_UART_Init                            0x080017c3   Thumb Code   114  stm32f4xx_hal_uart.o(i.HAL_UART_Init)
    HAL_UART_MspInit                         0x08001835   Thumb Code   354  usart.o(i.HAL_UART_MspInit)
    HAL_UART_Receive_DMA                     0x08001997   Thumb Code   200  stm32f4xx_hal_uart.o(i.HAL_UART_Receive_DMA)
    HAL_UART_RxCpltCallback                  0x08001a5f   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxCpltCallback)
    HAL_UART_RxHalfCpltCallback              0x08001a61   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_RxHalfCpltCallback)
    HAL_UART_Transmit_DMA                    0x08001a63   Thumb Code   160  stm32f4xx_hal_uart.o(i.HAL_UART_Transmit_DMA)
    HAL_UART_TxCpltCallback                  0x08001b03   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxCpltCallback)
    HAL_UART_TxHalfCpltCallback              0x08001b05   Thumb Code     2  stm32f4xx_hal_uart.o(i.HAL_UART_TxHalfCpltCallback)
    HardFault_Handler                        0x08001b07   Thumb Code     4  stm32f4xx_it.o(i.HardFault_Handler)
    MX_DMA_Init                              0x08001b0b   Thumb Code   156  dma.o(i.MX_DMA_Init)
    MX_GPIO_Init                             0x08001ba7   Thumb Code   108  gpio.o(i.MX_GPIO_Init)
    MX_USART1_UART_Init                      0x08001c13   Thumb Code    64  usart.o(i.MX_USART1_UART_Init)
    MemManage_Handler                        0x08001c53   Thumb Code     4  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001c57   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    PendSV_Handler                           0x08001c59   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    SVC_Handler                              0x08001c5b   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001c5d   Thumb Code     8  stm32f4xx_it.o(i.SysTick_Handler)
    SystemClock_Config                       0x08001c65   Thumb Code   194  main.o(i.SystemClock_Config)
    SystemInit                               0x08001d27   Thumb Code    40  system_stm32f4xx.o(i.SystemInit)
    USART1_IRQHandler                        0x080023c1   Thumb Code   144  stm32f4xx_it.o(i.USART1_IRQHandler)
    UsageFault_Handler                       0x08002451   Thumb Code     4  stm32f4xx_it.o(i.UsageFault_Handler)
    __scatterload_copy                       0x08002491   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800249f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x080024a1   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    main                                     0x080024af   Thumb Code   262  main.o(i.main)
    AHBPrescTable                            0x080025bc   Data          16  system_stm32f4xx.o(.constdata)
    APBPrescTable                            0x080025cc   Data           8  system_stm32f4xx.o(.constdata)
    Region$$Table$$Base                      0x080025d4   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080025f4   Number         0  anon$$obj.o(Region$$Table)
    rxFrameFlag                              0x20000000   Data           1  usart.o(.data)
    rxCount                                  0x20000001   Data           1  usart.o(.data)
    uwTick                                   0x20000004   Data           4  stm32f4xx_hal.o(.data)
    uwTickPrio                               0x20000008   Data           4  stm32f4xx_hal.o(.data)
    uwTickFreq                               0x2000000c   Data           1  stm32f4xx_hal.o(.data)
    SystemCoreClock                          0x20000010   Data           4  system_stm32f4xx.o(.data)
    hdma_memtomem_dma2_stream1               0x20000014   Data          96  dma.o(.bss)
    rxCmd                                    0x20000074   Data         255  usart.o(.bss)
    huart1                                   0x20000174   Data          64  usart.o(.bss)
    hdma_usart1_rx                           0x200001b4   Data          96  usart.o(.bss)
    hdma_usart1_tx                           0x20000214   Data          96  usart.o(.bss)
    MMCL_cmd                                 0x20000274   Data        1024  emm_v5.o(.bss)
    __initial_sp                             0x20001b48   Data           0  startup_stm32f407xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00002608, Max: 0x00080000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000025f4, Max: 0x00080000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO            3    RESET               startup_stm32f407xx.o
    0x08000188   0x08000188   0x00000000   Code   RO         2347  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         2354    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         2357    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         2359    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         2361    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         2362    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         2369    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         2364    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         2366    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         2355    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000024   Code   RO            4    .text               startup_stm32f407xx.o
    0x080001c4   0x080001c4   0x00000062   Code   RO         2350    .text               mc_w.l(uldiv.o)
    0x08000226   0x08000226   0x00000024   Code   RO         2352    .text               mc_w.l(memseta.o)
    0x0800024a   0x0800024a   0x0000001e   Code   RO         2370    .text               mc_w.l(llshl.o)
    0x08000268   0x08000268   0x00000020   Code   RO         2372    .text               mc_w.l(llushr.o)
    0x08000288   0x08000288   0x00000024   Code   RO         2374    .text               mc_w.l(init.o)
    0x080002ac   0x080002ac   0x00000004   Code   RO          255    i.BusFault_Handler  stm32f4xx_it.o
    0x080002b0   0x080002b0   0x00000010   Code   RO          256    i.DMA2_Stream2_IRQHandler  stm32f4xx_it.o
    0x080002c0   0x080002c0   0x00000010   Code   RO          257    i.DMA2_Stream7_IRQHandler  stm32f4xx_it.o
    0x080002d0   0x080002d0   0x00000034   Code   RO         1633    i.DMA_CalcBaseAndBitshift  stm32f4xx_hal_dma.o
    0x08000304   0x08000304   0x000000aa   Code   RO         1634    i.DMA_CheckFifoParam  stm32f4xx_hal_dma.o
    0x080003ae   0x080003ae   0x0000002c   Code   RO         1635    i.DMA_SetConfig     stm32f4xx_hal_dma.o
    0x080003da   0x080003da   0x00000002   Code   RO          258    i.DebugMon_Handler  stm32f4xx_it.o
    0x080003dc   0x080003dc   0x0000003c   Code   RO          374    i.Emm_V5_En_Control  emm_v5.o
    0x08000418   0x08000418   0x00000066   Code   RO          414    i.Emm_V5_Pos_Control  emm_v5.o
    0x0800047e   0x0800047e   0x00000004   Code   RO           13    i.Error_Handler     main.o
    0x08000482   0x08000482   0x000000aa   Code   RO         1636    i.HAL_DMA_Abort     stm32f4xx_hal_dma.o
    0x0800052c   0x0800052c   0x00000028   Code   RO         1637    i.HAL_DMA_Abort_IT  stm32f4xx_hal_dma.o
    0x08000554   0x08000554   0x00000240   Code   RO         1641    i.HAL_DMA_IRQHandler  stm32f4xx_hal_dma.o
    0x08000794   0x08000794   0x000000ee   Code   RO         1642    i.HAL_DMA_Init      stm32f4xx_hal_dma.o
    0x08000882   0x08000882   0x00000092   Code   RO         1646    i.HAL_DMA_Start_IT  stm32f4xx_hal_dma.o
    0x08000914   0x08000914   0x0000002a   Code   RO         2062    i.HAL_Delay         stm32f4xx_hal.o
    0x0800093e   0x0800093e   0x00000232   Code   RO         1529    i.HAL_GPIO_Init     stm32f4xx_hal_gpio.o
    0x08000b70   0x08000b70   0x0000000c   Code   RO         2068    i.HAL_GetTick       stm32f4xx_hal.o
    0x08000b7c   0x08000b7c   0x00000022   Code   RO         2074    i.HAL_IncTick       stm32f4xx_hal.o
    0x08000b9e   0x08000b9e   0x00000042   Code   RO         2075    i.HAL_Init          stm32f4xx_hal.o
    0x08000be0   0x08000be0   0x00000052   Code   RO         2076    i.HAL_InitTick      stm32f4xx_hal.o
    0x08000c32   0x08000c32   0x00000050   Code   RO          349    i.HAL_MspInit       stm32f4xx_hal_msp.o
    0x08000c82   0x08000c82   0x00000020   Code   RO         1920    i.HAL_NVIC_EnableIRQ  stm32f4xx_hal_cortex.o
    0x08000ca2   0x08000ca2   0x0000007c   Code   RO         1926    i.HAL_NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08000d1e   0x08000d1e   0x00000030   Code   RO         1927    i.HAL_NVIC_SetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08000d4e   0x08000d4e   0x000001fa   Code   RO         1130    i.HAL_RCC_ClockConfig  stm32f4xx_hal_rcc.o
    0x08000f48   0x08000f48   0x0000000c   Code   RO         1135    i.HAL_RCC_GetHCLKFreq  stm32f4xx_hal_rcc.o
    0x08000f54   0x08000f54   0x00000022   Code   RO         1137    i.HAL_RCC_GetPCLK1Freq  stm32f4xx_hal_rcc.o
    0x08000f76   0x08000f76   0x00000022   Code   RO         1138    i.HAL_RCC_GetPCLK2Freq  stm32f4xx_hal_rcc.o
    0x08000f98   0x08000f98   0x000000d4   Code   RO         1139    i.HAL_RCC_GetSysClockFreq  stm32f4xx_hal_rcc.o
    0x0800106c   0x0800106c   0x00000564   Code   RO         1142    i.HAL_RCC_OscConfig  stm32f4xx_hal_rcc.o
    0x080015d0   0x080015d0   0x00000034   Code   RO         1931    i.HAL_SYSTICK_Config  stm32f4xx_hal_cortex.o
    0x08001604   0x08001604   0x00000066   Code   RO          817    i.HAL_UART_DMAStop  stm32f4xx_hal_uart.o
    0x0800166a   0x0800166a   0x00000002   Code   RO          819    i.HAL_UART_ErrorCallback  stm32f4xx_hal_uart.o
    0x0800166c   0x0800166c   0x00000156   Code   RO          822    i.HAL_UART_IRQHandler  stm32f4xx_hal_uart.o
    0x080017c2   0x080017c2   0x00000072   Code   RO          823    i.HAL_UART_Init     stm32f4xx_hal_uart.o
    0x08001834   0x08001834   0x00000162   Code   RO          213    i.HAL_UART_MspInit  usart.o
    0x08001996   0x08001996   0x000000c8   Code   RO          827    i.HAL_UART_Receive_DMA  stm32f4xx_hal_uart.o
    0x08001a5e   0x08001a5e   0x00000002   Code   RO          829    i.HAL_UART_RxCpltCallback  stm32f4xx_hal_uart.o
    0x08001a60   0x08001a60   0x00000002   Code   RO          830    i.HAL_UART_RxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08001a62   0x08001a62   0x000000a0   Code   RO          832    i.HAL_UART_Transmit_DMA  stm32f4xx_hal_uart.o
    0x08001b02   0x08001b02   0x00000002   Code   RO          834    i.HAL_UART_TxCpltCallback  stm32f4xx_hal_uart.o
    0x08001b04   0x08001b04   0x00000002   Code   RO          835    i.HAL_UART_TxHalfCpltCallback  stm32f4xx_hal_uart.o
    0x08001b06   0x08001b06   0x00000004   Code   RO          259    i.HardFault_Handler  stm32f4xx_it.o
    0x08001b0a   0x08001b0a   0x0000009c   Code   RO          182    i.MX_DMA_Init       dma.o
    0x08001ba6   0x08001ba6   0x0000006c   Code   RO          158    i.MX_GPIO_Init      gpio.o
    0x08001c12   0x08001c12   0x00000040   Code   RO          214    i.MX_USART1_UART_Init  usart.o
    0x08001c52   0x08001c52   0x00000004   Code   RO          260    i.MemManage_Handler  stm32f4xx_it.o
    0x08001c56   0x08001c56   0x00000002   Code   RO          261    i.NMI_Handler       stm32f4xx_it.o
    0x08001c58   0x08001c58   0x00000002   Code   RO          262    i.PendSV_Handler    stm32f4xx_it.o
    0x08001c5a   0x08001c5a   0x00000002   Code   RO          263    i.SVC_Handler       stm32f4xx_it.o
    0x08001c5c   0x08001c5c   0x00000008   Code   RO          264    i.SysTick_Handler   stm32f4xx_it.o
    0x08001c64   0x08001c64   0x000000c2   Code   RO           14    i.SystemClock_Config  main.o
    0x08001d26   0x08001d26   0x00000028   Code   RO         2314    i.SystemInit        system_stm32f4xx.o
    0x08001d4e   0x08001d4e   0x00000014   Code   RO          836    i.UART_DMAAbortOnError  stm32f4xx_hal_uart.o
    0x08001d62   0x08001d62   0x00000050   Code   RO          837    i.UART_DMAError     stm32f4xx_hal_uart.o
    0x08001db2   0x08001db2   0x00000046   Code   RO          838    i.UART_DMAReceiveCplt  stm32f4xx_hal_uart.o
    0x08001df8   0x08001df8   0x0000000e   Code   RO          840    i.UART_DMARxHalfCplt  stm32f4xx_hal_uart.o
    0x08001e06   0x08001e06   0x00000036   Code   RO          842    i.UART_DMATransmitCplt  stm32f4xx_hal_uart.o
    0x08001e3c   0x08001e3c   0x0000000e   Code   RO          844    i.UART_DMATxHalfCplt  stm32f4xx_hal_uart.o
    0x08001e4a   0x08001e4a   0x00000020   Code   RO          846    i.UART_EndRxTransfer  stm32f4xx_hal_uart.o
    0x08001e6a   0x08001e6a   0x00000020   Code   RO          847    i.UART_EndTransmit_IT  stm32f4xx_hal_uart.o
    0x08001e8a   0x08001e8a   0x00000014   Code   RO          848    i.UART_EndTxTransfer  stm32f4xx_hal_uart.o
    0x08001e9e   0x08001e9e   0x000000a6   Code   RO          849    i.UART_Receive_IT   stm32f4xx_hal_uart.o
    0x08001f44   0x08001f44   0x00000414   Code   RO          850    i.UART_SetConfig    stm32f4xx_hal_uart.o
    0x08002358   0x08002358   0x00000068   Code   RO          851    i.UART_Transmit_IT  stm32f4xx_hal_uart.o
    0x080023c0   0x080023c0   0x00000090   Code   RO          265    i.USART1_IRQHandler  stm32f4xx_it.o
    0x08002450   0x08002450   0x00000004   Code   RO          266    i.UsageFault_Handler  stm32f4xx_it.o
    0x08002454   0x08002454   0x00000010   Code   RO         1933    i.__NVIC_GetPriorityGrouping  stm32f4xx_hal_cortex.o
    0x08002464   0x08002464   0x0000002c   Code   RO         1934    i.__NVIC_SetPriority  stm32f4xx_hal_cortex.o
    0x08002490   0x08002490   0x0000000e   Code   RO         2378    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800249e   0x0800249e   0x00000002   Code   RO         2379    i.__scatterload_null  mc_w.l(handlers.o)
    0x080024a0   0x080024a0   0x0000000e   Code   RO         2380    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x080024ae   0x080024ae   0x00000106   Code   RO           15    i.main              main.o
    0x080025b4   0x080025b4   0x00000008   Data   RO         1648    .constdata          stm32f4xx_hal_dma.o
    0x080025bc   0x080025bc   0x00000018   Data   RO         2315    .constdata          system_stm32f4xx.o
    0x080025d4   0x080025d4   0x00000020   Data   RO         2376    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080025f4, Size: 0x00001b48, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080025f4   0x00000002   Data   RW          216    .data               usart.o
    0x20000002   0x080025f6   0x00000002   PAD
    0x20000004   0x080025f8   0x00000009   Data   RW         2082    .data               stm32f4xx_hal.o
    0x2000000d   0x08002601   0x00000003   PAD
    0x20000010   0x08002604   0x00000004   Data   RW         2316    .data               system_stm32f4xx.o
    0x20000014        -       0x00000060   Zero   RW          183    .bss                dma.o
    0x20000074        -       0x00000200   Zero   RW          215    .bss                usart.o
    0x20000274        -       0x000008d4   Zero   RW          433    .bss                emm_v5.o
    0x20000b48        -       0x00001000   Zero   RW            1    STACK               startup_stm32f407xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       156          0          0          0         96       1411   dma.o
       162          0          0          0       2260      31394   emm_v5.o
       108          0          0          0          0       1067   gpio.o
       460          0          0          0          0     658212   main.o
        36          8        392          0       4096        932   startup_stm32f407xx.o
       236          0          0          9          0      10389   stm32f4xx_hal.o
       316          0          0          0          0      35339   stm32f4xx_hal_cortex.o
      1436          0          8          0          0       8146   stm32f4xx_hal_dma.o
       562          0          0          0          0       1900   stm32f4xx_hal_gpio.o
        80          0          0          0          0       1078   stm32f4xx_hal_msp.o
      2178          0          0          0          0       6476   stm32f4xx_hal_rcc.o
      2578          0          0          0          0      18053   stm32f4xx_hal_uart.o
       208          0          0          0          0       7678   stm32f4xx_it.o
        40          0         24          4          0       1503   system_stm32f4xx.o
       418          0          0          2        512       2494   usart.o

    ----------------------------------------------------------------------
      8974          <USER>        <GROUP>         20       6964     786072   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         0          0          0          5          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
        30          0          0          0          0         68   llshl.o
        32          0          0          0          0         68   llushr.o
        36          0          0          0          0        108   memseta.o
        98          0          0          0          0         92   uldiv.o

    ----------------------------------------------------------------------
       286         <USER>          <GROUP>          0          0        404   Library Totals
         0          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       286         16          0          0          0        404   mc_w.l

    ----------------------------------------------------------------------
       286         <USER>          <GROUP>          0          0        404   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

      9260         24        456         20       6964     780176   Grand Totals
      9260         24        456         20       6964     780176   ELF Image Totals
      9260         24        456         20          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                 9716 (   9.49kB)
    Total RW  Size (RW Data + ZI Data)              6984 (   6.82kB)
    Total ROM Size (Code + RO Data + RW Data)       9736 (   9.51kB)

==============================================================================

